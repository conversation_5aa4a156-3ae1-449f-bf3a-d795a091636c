<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Multi-Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .nav-tab.active {
            background: white;
            border-bottom-color: #4facfe;
            color: #4facfe;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 500px;
        }

        .tab-content.active {
            display: block;
        }

        .tool-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .tool-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .result-area {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .color-preview {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            border: 1px solid #ccc;
            display: inline-block;
            margin-left: 10px;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .slider {
            flex: 1;
        }

        .slider-value {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ Interactive Multi-Tool</h1>
            <p>A comprehensive collection of useful utilities and calculators</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('text-tools')">📝 Text Tools</button>
            <button class="nav-tab" onclick="showTab('calculators')">🧮 Calculators</button>
            <button class="nav-tab" onclick="showTab('converters')">🔄 Converters</button>
            <button class="nav-tab" onclick="showTab('generators')">⚡ Generators</button>
            <button class="nav-tab" onclick="showTab('utilities')">🔧 Utilities</button>
        </div>

        <!-- Text Tools Tab -->
        <div id="text-tools" class="tab-content active">
            <div class="grid">
                <div class="card">
                    <h3>📊 Text Analyzer</h3>
                    <div class="form-group">
                        <label for="textInput">Enter your text:</label>
                        <textarea id="textInput" class="form-control" placeholder="Type or paste your text here..." oninput="analyzeText()"></textarea>
                    </div>
                    <div id="textStats" class="result-area">
                        Characters: 0 | Words: 0 | Lines: 0 | Paragraphs: 0
                    </div>
                </div>

                <div class="card">
                    <h3>🔄 Text Transformer</h3>
                    <div class="form-group">
                        <label for="transformInput">Text to transform:</label>
                        <textarea id="transformInput" class="form-control" placeholder="Enter text to transform..."></textarea>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="transformText('upper')">UPPERCASE</button>
                        <button class="btn btn-primary" onclick="transformText('lower')">lowercase</button>
                        <button class="btn btn-primary" onclick="transformText('title')">Title Case</button>
                        <button class="btn btn-primary" onclick="transformText('reverse')">esreveR</button>
                    </div>
                    <div id="transformResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>🔍 Find & Replace</h3>
                    <div class="form-group">
                        <label for="findReplaceText">Text:</label>
                        <textarea id="findReplaceText" class="form-control" placeholder="Enter your text..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="findText">Find:</label>
                        <input type="text" id="findText" class="form-control" placeholder="Text to find...">
                    </div>
                    <div class="form-group">
                        <label for="replaceText">Replace with:</label>
                        <input type="text" id="replaceText" class="form-control" placeholder="Replacement text...">
                    </div>
                    <button class="btn btn-primary" onclick="findReplace()">Replace All</button>
                    <div id="findReplaceResult" class="result-area"></div>
                </div>
            </div>
        </div>

        <!-- Calculators Tab -->
        <div id="calculators" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>🧮 Basic Calculator</h3>
                    <div class="form-group">
                        <label for="calcDisplay">Calculator Display:</label>
                        <input type="text" id="calcDisplay" class="form-control" readonly style="font-size: 18px; text-align: right;">
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                        <button class="btn btn-secondary" onclick="clearCalc()">C</button>
                        <button class="btn btn-secondary" onclick="appendToCalc('/')">/</button>
                        <button class="btn btn-secondary" onclick="appendToCalc('*')">×</button>
                        <button class="btn btn-secondary" onclick="deleteLast()">⌫</button>

                        <button class="btn btn-primary" onclick="appendToCalc('7')">7</button>
                        <button class="btn btn-primary" onclick="appendToCalc('8')">8</button>
                        <button class="btn btn-primary" onclick="appendToCalc('9')">9</button>
                        <button class="btn btn-secondary" onclick="appendToCalc('-')">-</button>

                        <button class="btn btn-primary" onclick="appendToCalc('4')">4</button>
                        <button class="btn btn-primary" onclick="appendToCalc('5')">5</button>
                        <button class="btn btn-primary" onclick="appendToCalc('6')">6</button>
                        <button class="btn btn-secondary" onclick="appendToCalc('+')">+</button>

                        <button class="btn btn-primary" onclick="appendToCalc('1')">1</button>
                        <button class="btn btn-primary" onclick="appendToCalc('2')">2</button>
                        <button class="btn btn-primary" onclick="appendToCalc('3')">3</button>
                        <button class="btn btn-secondary" onclick="calculate()" style="grid-row: span 2;">=</button>

                        <button class="btn btn-primary" onclick="appendToCalc('0')" style="grid-column: span 2;">0</button>
                        <button class="btn btn-primary" onclick="appendToCalc('.')">.</button>
                    </div>
                </div>

                <div class="card">
                    <h3>📐 Geometry Calculator</h3>
                    <div class="form-group">
                        <label for="shapeSelect">Select Shape:</label>
                        <select id="shapeSelect" class="form-control" onchange="showShapeInputs()">
                            <option value="circle">Circle</option>
                            <option value="rectangle">Rectangle</option>
                            <option value="triangle">Triangle</option>
                            <option value="square">Square</option>
                        </select>
                    </div>
                    <div id="shapeInputs"></div>
                    <button class="btn btn-primary" onclick="calculateGeometry()">Calculate</button>
                    <div id="geometryResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>💰 Financial Calculator</h3>
                    <div class="form-group">
                        <label for="principal">Principal Amount ($):</label>
                        <input type="number" id="principal" class="form-control" placeholder="10000">
                    </div>
                    <div class="form-group">
                        <label for="rate">Annual Interest Rate (%):</label>
                        <input type="number" id="rate" class="form-control" placeholder="5" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="time">Time Period (years):</label>
                        <input type="number" id="time" class="form-control" placeholder="10">
                    </div>
                    <div class="form-group">
                        <label for="compound">Compound Frequency:</label>
                        <select id="compound" class="form-control">
                            <option value="1">Annually</option>
                            <option value="2">Semi-annually</option>
                            <option value="4">Quarterly</option>
                            <option value="12">Monthly</option>
                            <option value="365">Daily</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="calculateCompoundInterest()">Calculate</button>
                    <div id="financialResult" class="result-area"></div>
                </div>
            </div>
        </div>

        <!-- Converters Tab -->
        <div id="converters" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>🌡️ Temperature Converter</h3>
                    <div class="form-group">
                        <label for="tempValue">Temperature Value:</label>
                        <input type="number" id="tempValue" class="form-control" placeholder="Enter temperature..." oninput="convertTemperature()">
                    </div>
                    <div class="form-group">
                        <label for="tempFrom">From:</label>
                        <select id="tempFrom" class="form-control" onchange="convertTemperature()">
                            <option value="celsius">Celsius (°C)</option>
                            <option value="fahrenheit">Fahrenheit (°F)</option>
                            <option value="kelvin">Kelvin (K)</option>
                        </select>
                    </div>
                    <div id="tempResults" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>📏 Unit Converter</h3>
                    <div class="form-group">
                        <label for="unitValue">Value:</label>
                        <input type="number" id="unitValue" class="form-control" placeholder="Enter value..." oninput="convertUnits()">
                    </div>
                    <div class="form-group">
                        <label for="unitType">Unit Type:</label>
                        <select id="unitType" class="form-control" onchange="updateUnitOptions()">
                            <option value="length">Length</option>
                            <option value="weight">Weight</option>
                            <option value="volume">Volume</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="unitFrom">From:</label>
                        <select id="unitFrom" class="form-control" onchange="convertUnits()"></select>
                    </div>
                    <div class="form-group">
                        <label for="unitTo">To:</label>
                        <select id="unitTo" class="form-control" onchange="convertUnits()"></select>
                    </div>
                    <div id="unitResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>🔢 Number Base Converter</h3>
                    <div class="form-group">
                        <label for="numberInput">Number:</label>
                        <input type="text" id="numberInput" class="form-control" placeholder="Enter number..." oninput="convertBases()">
                    </div>
                    <div class="form-group">
                        <label for="baseFrom">From Base:</label>
                        <select id="baseFrom" class="form-control" onchange="convertBases()">
                            <option value="10">Decimal (10)</option>
                            <option value="2">Binary (2)</option>
                            <option value="8">Octal (8)</option>
                            <option value="16">Hexadecimal (16)</option>
                        </select>
                    </div>
                    <div id="baseResults" class="result-area"></div>
                </div>
            </div>
        </div>

        <!-- Generators Tab -->
        <div id="generators" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>🔐 Password Generator</h3>
                    <div class="form-group">
                        <label for="passwordLength">Password Length:</label>
                        <div class="slider-container">
                            <input type="range" id="passwordLength" class="slider" min="4" max="50" value="12" oninput="updatePasswordLength()">
                            <span id="passwordLengthValue" class="slider-value">12</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="includeUppercase" checked> Include Uppercase (A-Z)
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="includeLowercase" checked> Include Lowercase (a-z)
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="includeNumbers" checked> Include Numbers (0-9)
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="includeSymbols"> Include Symbols (!@#$%^&*)
                        </label>
                    </div>
                    <button class="btn btn-primary" onclick="generatePassword()">Generate Password</button>
                    <div id="passwordResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>🎨 Color Generator</h3>
                    <div class="form-group">
                        <label for="colorType">Color Type:</label>
                        <select id="colorType" class="form-control" onchange="generateColor()">
                            <option value="random">Random Color</option>
                            <option value="palette">Color Palette</option>
                            <option value="gradient">Gradient</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="generateColor()">Generate</button>
                    <div id="colorResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>📝 Lorem Ipsum Generator</h3>
                    <div class="form-group">
                        <label for="loremType">Generate:</label>
                        <select id="loremType" class="form-control">
                            <option value="words">Words</option>
                            <option value="sentences">Sentences</option>
                            <option value="paragraphs">Paragraphs</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="loremCount">Count:</label>
                        <input type="number" id="loremCount" class="form-control" value="5" min="1" max="100">
                    </div>
                    <button class="btn btn-primary" onclick="generateLorem()">Generate</button>
                    <div id="loremResult" class="result-area"></div>
                </div>
            </div>
        </div>

        <!-- Utilities Tab -->
        <div id="utilities" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>🕒 Time Zone Converter</h3>
                    <div class="form-group">
                        <label for="timeInput">Time:</label>
                        <input type="datetime-local" id="timeInput" class="form-control" onchange="convertTimeZones()">
                    </div>
                    <div class="form-group">
                        <label for="fromTimezone">From Timezone:</label>
                        <select id="fromTimezone" class="form-control" onchange="convertTimeZones()">
                            <option value="UTC">UTC</option>
                            <option value="America/New_York">Eastern Time (ET)</option>
                            <option value="America/Chicago">Central Time (CT)</option>
                            <option value="America/Denver">Mountain Time (MT)</option>
                            <option value="America/Los_Angeles">Pacific Time (PT)</option>
                            <option value="Europe/London">London (GMT)</option>
                            <option value="Europe/Paris">Paris (CET)</option>
                            <option value="Asia/Tokyo">Tokyo (JST)</option>
                        </select>
                    </div>
                    <div id="timezoneResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>📊 QR Code Generator</h3>
                    <div class="form-group">
                        <label for="qrText">Text to encode:</label>
                        <textarea id="qrText" class="form-control" placeholder="Enter text, URL, or data..."></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="generateQR()">Generate QR Code</button>
                    <div id="qrResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>🔗 URL Shortener Simulator</h3>
                    <div class="form-group">
                        <label for="longUrl">Long URL:</label>
                        <input type="url" id="longUrl" class="form-control" placeholder="https://example.com/very/long/url">
                    </div>
                    <button class="btn btn-primary" onclick="shortenUrl()">Shorten URL</button>
                    <div id="urlResult" class="result-area"></div>
                </div>

                <div class="card">
                    <h3>🎲 Random Number Generator</h3>
                    <div class="form-group">
                        <label for="minNumber">Minimum:</label>
                        <input type="number" id="minNumber" class="form-control" value="1">
                    </div>
                    <div class="form-group">
                        <label for="maxNumber">Maximum:</label>
                        <input type="number" id="maxNumber" class="form-control" value="100">
                    </div>
                    <div class="form-group">
                        <label for="numberCount">How many numbers:</label>
                        <input type="number" id="numberCount" class="form-control" value="1" min="1" max="100">
                    </div>
                    <button class="btn btn-primary" onclick="generateRandomNumbers()">Generate</button>
                    <div id="randomResult" class="result-area"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Text Tools Functions
        function analyzeText() {
            const text = document.getElementById('textInput').value;
            const chars = text.length;
            const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
            const lines = text.split('\n').length;
            const paragraphs = text.trim() === '' ? 0 : text.split(/\n\s*\n/).length;

            document.getElementById('textStats').textContent =
                `Characters: ${chars} | Words: ${words} | Lines: ${lines} | Paragraphs: ${paragraphs}`;
        }

        function transformText(type) {
            const text = document.getElementById('transformInput').value;
            let result = '';

            switch(type) {
                case 'upper':
                    result = text.toUpperCase();
                    break;
                case 'lower':
                    result = text.toLowerCase();
                    break;
                case 'title':
                    result = text.replace(/\w\S*/g, (txt) =>
                        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
                    break;
                case 'reverse':
                    result = text.split('').reverse().join('');
                    break;
            }

            document.getElementById('transformResult').textContent = result;
        }

        function findReplace() {
            const text = document.getElementById('findReplaceText').value;
            const findText = document.getElementById('findText').value;
            const replaceText = document.getElementById('replaceText').value;

            if (!findText) {
                document.getElementById('findReplaceResult').textContent = 'Please enter text to find.';
                return;
            }

            const result = text.replaceAll(findText, replaceText);
            const count = (text.match(new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;

            document.getElementById('findReplaceResult').textContent =
                `Replaced ${count} occurrence(s):\n\n${result}`;
        }

        // Calculator Functions
        let calcExpression = '';

        function appendToCalc(value) {
            if (value === '*') value = '*';
            calcExpression += value;
            document.getElementById('calcDisplay').value = calcExpression;
        }

        function clearCalc() {
            calcExpression = '';
            document.getElementById('calcDisplay').value = '';
        }

        function deleteLast() {
            calcExpression = calcExpression.slice(0, -1);
            document.getElementById('calcDisplay').value = calcExpression;
        }

        function calculate() {
            try {
                const result = eval(calcExpression.replace(/×/g, '*'));
                document.getElementById('calcDisplay').value = result;
                calcExpression = result.toString();
            } catch (error) {
                document.getElementById('calcDisplay').value = 'Error';
                calcExpression = '';
            }
        }

        // Financial Calculator
        function calculateCompoundInterest() {
            const principal = parseFloat(document.getElementById('principal').value);
            const rate = parseFloat(document.getElementById('rate').value) / 100;
            const time = parseFloat(document.getElementById('time').value);
            const compound = parseFloat(document.getElementById('compound').value);

            if (principal && rate && time && compound) {
                const amount = principal * Math.pow((1 + rate / compound), compound * time);
                const interest = amount - principal;

                document.getElementById('financialResult').textContent =
                    `Final Amount: $${amount.toFixed(2)}\nInterest Earned: $${interest.toFixed(2)}`;
            } else {
                document.getElementById('financialResult').textContent = 'Please enter valid values.';
            }
        }

        // Geometry Calculator
        function showShapeInputs() {
            const shape = document.getElementById('shapeSelect').value;
            const inputsDiv = document.getElementById('shapeInputs');

            let html = '';
            switch(shape) {
                case 'circle':
                    html = '<div class="form-group"><label>Radius:</label><input type="number" id="radius" class="form-control" placeholder="Enter radius"></div>';
                    break;
                case 'rectangle':
                    html = '<div class="form-group"><label>Length:</label><input type="number" id="length" class="form-control" placeholder="Enter length"></div>' +
                           '<div class="form-group"><label>Width:</label><input type="number" id="width" class="form-control" placeholder="Enter width"></div>';
                    break;
                case 'triangle':
                    html = '<div class="form-group"><label>Base:</label><input type="number" id="base" class="form-control" placeholder="Enter base"></div>' +
                           '<div class="form-group"><label>Height:</label><input type="number" id="height" class="form-control" placeholder="Enter height"></div>';
                    break;
                case 'square':
                    html = '<div class="form-group"><label>Side:</label><input type="number" id="side" class="form-control" placeholder="Enter side length"></div>';
                    break;
            }
            inputsDiv.innerHTML = html;
        }

        function calculateGeometry() {
            const shape = document.getElementById('shapeSelect').value;
            let result = '';

            switch(shape) {
                case 'circle':
                    const radius = parseFloat(document.getElementById('radius').value);
                    if (radius) {
                        const area = Math.PI * radius * radius;
                        const circumference = 2 * Math.PI * radius;
                        result = `Area: ${area.toFixed(2)}\nCircumference: ${circumference.toFixed(2)}`;
                    }
                    break;
                case 'rectangle':
                    const length = parseFloat(document.getElementById('length').value);
                    const width = parseFloat(document.getElementById('width').value);
                    if (length && width) {
                        const area = length * width;
                        const perimeter = 2 * (length + width);
                        result = `Area: ${area.toFixed(2)}\nPerimeter: ${perimeter.toFixed(2)}`;
                    }
                    break;
                case 'triangle':
                    const base = parseFloat(document.getElementById('base').value);
                    const height = parseFloat(document.getElementById('height').value);
                    if (base && height) {
                        const area = 0.5 * base * height;
                        result = `Area: ${area.toFixed(2)}`;
                    }
                    break;
                case 'square':
                    const side = parseFloat(document.getElementById('side').value);
                    if (side) {
                        const area = side * side;
                        const perimeter = 4 * side;
                        result = `Area: ${area.toFixed(2)}\nPerimeter: ${perimeter.toFixed(2)}`;
                    }
                    break;
            }

            document.getElementById('geometryResult').textContent = result || 'Please enter valid values.';
        }

        // Initialize geometry inputs
        showShapeInputs();

        // Temperature Converter
        function convertTemperature() {
            const value = parseFloat(document.getElementById('tempValue').value);
            const from = document.getElementById('tempFrom').value;

            if (isNaN(value)) {
                document.getElementById('tempResults').textContent = 'Please enter a valid temperature.';
                return;
            }

            let celsius, fahrenheit, kelvin;

            switch(from) {
                case 'celsius':
                    celsius = value;
                    fahrenheit = (value * 9/5) + 32;
                    kelvin = value + 273.15;
                    break;
                case 'fahrenheit':
                    celsius = (value - 32) * 5/9;
                    fahrenheit = value;
                    kelvin = celsius + 273.15;
                    break;
                case 'kelvin':
                    celsius = value - 273.15;
                    fahrenheit = (celsius * 9/5) + 32;
                    kelvin = value;
                    break;
            }

            document.getElementById('tempResults').textContent =
                `Celsius: ${celsius.toFixed(2)}°C\nFahrenheit: ${fahrenheit.toFixed(2)}°F\nKelvin: ${kelvin.toFixed(2)}K`;
        }

        // Unit Converter
        const unitConversions = {
            length: {
                meter: 1,
                kilometer: 0.001,
                centimeter: 100,
                millimeter: 1000,
                inch: 39.3701,
                foot: 3.28084,
                yard: 1.09361,
                mile: 0.000621371
            },
            weight: {
                kilogram: 1,
                gram: 1000,
                pound: 2.20462,
                ounce: 35.274,
                ton: 0.001
            },
            volume: {
                liter: 1,
                milliliter: 1000,
                gallon: 0.264172,
                quart: 1.05669,
                pint: 2.11338,
                cup: 4.22675,
                fluid_ounce: 33.814
            }
        };

        function updateUnitOptions() {
            const unitType = document.getElementById('unitType').value;
            const fromSelect = document.getElementById('unitFrom');
            const toSelect = document.getElementById('unitTo');

            fromSelect.innerHTML = '';
            toSelect.innerHTML = '';

            Object.keys(unitConversions[unitType]).forEach(unit => {
                const option1 = new Option(unit.replace('_', ' '), unit);
                const option2 = new Option(unit.replace('_', ' '), unit);
                fromSelect.add(option1);
                toSelect.add(option2);
            });

            convertUnits();
        }

        function convertUnits() {
            const value = parseFloat(document.getElementById('unitValue').value);
            const unitType = document.getElementById('unitType').value;
            const fromUnit = document.getElementById('unitFrom').value;
            const toUnit = document.getElementById('unitTo').value;

            if (isNaN(value) || !fromUnit || !toUnit) {
                document.getElementById('unitResult').textContent = 'Please enter valid values.';
                return;
            }

            const fromFactor = unitConversions[unitType][fromUnit];
            const toFactor = unitConversions[unitType][toUnit];
            const result = (value / fromFactor) * toFactor;

            document.getElementById('unitResult').textContent =
                `${value} ${fromUnit.replace('_', ' ')} = ${result.toFixed(6)} ${toUnit.replace('_', ' ')}`;
        }

        // Initialize unit converter
        updateUnitOptions();

        // Number Base Converter
        function convertBases() {
            const number = document.getElementById('numberInput').value.trim();
            const fromBase = parseInt(document.getElementById('baseFrom').value);

            if (!number) {
                document.getElementById('baseResults').textContent = 'Please enter a number.';
                return;
            }

            try {
                const decimal = parseInt(number, fromBase);
                if (isNaN(decimal)) {
                    document.getElementById('baseResults').textContent = 'Invalid number for the selected base.';
                    return;
                }

                const binary = decimal.toString(2);
                const octal = decimal.toString(8);
                const decimalStr = decimal.toString(10);
                const hexadecimal = decimal.toString(16).toUpperCase();

                document.getElementById('baseResults').textContent =
                    `Binary (2): ${binary}\nOctal (8): ${octal}\nDecimal (10): ${decimalStr}\nHexadecimal (16): ${hexadecimal}`;
            } catch (error) {
                document.getElementById('baseResults').textContent = 'Error converting number.';
            }
        }

        // Password Generator
        function updatePasswordLength() {
            const length = document.getElementById('passwordLength').value;
            document.getElementById('passwordLengthValue').textContent = length;
        }

        function generatePassword() {
            const length = parseInt(document.getElementById('passwordLength').value);
            const includeUppercase = document.getElementById('includeUppercase').checked;
            const includeLowercase = document.getElementById('includeLowercase').checked;
            const includeNumbers = document.getElementById('includeNumbers').checked;
            const includeSymbols = document.getElementById('includeSymbols').checked;

            let charset = '';
            if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
            if (includeNumbers) charset += '0123456789';
            if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

            if (!charset) {
                document.getElementById('passwordResult').textContent = 'Please select at least one character type.';
                return;
            }

            let password = '';
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }

            document.getElementById('passwordResult').textContent = password;
        }

        // Color Generator
        function generateColor() {
            const colorType = document.getElementById('colorType').value;
            let result = '';

            switch(colorType) {
                case 'random':
                    const hex = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
                    const rgb = hexToRgb(hex);
                    result = `Hex: ${hex}\nRGB: rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
                    break;
                case 'palette':
                    const colors = [];
                    for (let i = 0; i < 5; i++) {
                        colors.push('#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0'));
                    }
                    result = 'Color Palette:\n' + colors.join('\n');
                    break;
                case 'gradient':
                    const color1 = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
                    const color2 = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
                    result = `Gradient: ${color1} to ${color2}\nCSS: linear-gradient(45deg, ${color1}, ${color2})`;
                    break;
            }

            document.getElementById('colorResult').textContent = result;
        }

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        // Lorem Ipsum Generator
        const loremWords = ['lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit', 'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore', 'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud', 'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo', 'consequat', 'duis', 'aute', 'irure', 'in', 'reprehenderit', 'voluptate', 'velit', 'esse', 'cillum', 'fugiat', 'nulla', 'pariatur', 'excepteur', 'sint', 'occaecat', 'cupidatat', 'non', 'proident', 'sunt', 'culpa', 'qui', 'officia', 'deserunt', 'mollit', 'anim', 'id', 'est', 'laborum'];

        function generateLorem() {
            const type = document.getElementById('loremType').value;
            const count = parseInt(document.getElementById('loremCount').value);
            let result = '';

            switch(type) {
                case 'words':
                    const words = [];
                    for (let i = 0; i < count; i++) {
                        words.push(loremWords[Math.floor(Math.random() * loremWords.length)]);
                    }
                    result = words.join(' ');
                    break;
                case 'sentences':
                    const sentences = [];
                    for (let i = 0; i < count; i++) {
                        const sentenceLength = Math.floor(Math.random() * 10) + 5;
                        const words = [];
                        for (let j = 0; j < sentenceLength; j++) {
                            words.push(loremWords[Math.floor(Math.random() * loremWords.length)]);
                        }
                        words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
                        sentences.push(words.join(' ') + '.');
                    }
                    result = sentences.join(' ');
                    break;
                case 'paragraphs':
                    const paragraphs = [];
                    for (let i = 0; i < count; i++) {
                        const sentenceCount = Math.floor(Math.random() * 5) + 3;
                        const sentences = [];
                        for (let j = 0; j < sentenceCount; j++) {
                            const sentenceLength = Math.floor(Math.random() * 10) + 5;
                            const words = [];
                            for (let k = 0; k < sentenceLength; k++) {
                                words.push(loremWords[Math.floor(Math.random() * loremWords.length)]);
                            }
                            words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
                            sentences.push(words.join(' ') + '.');
                        }
                        paragraphs.push(sentences.join(' '));
                    }
                    result = paragraphs.join('\n\n');
                    break;
            }

            document.getElementById('loremResult').textContent = result;
        }

        // Time Zone Converter
        function convertTimeZones() {
            const timeInput = document.getElementById('timeInput').value;
            const fromTimezone = document.getElementById('fromTimezone').value;

            if (!timeInput) {
                document.getElementById('timezoneResult').textContent = 'Please select a date and time.';
                return;
            }

            const date = new Date(timeInput);
            const timezones = [
                { name: 'UTC', zone: 'UTC' },
                { name: 'Eastern Time', zone: 'America/New_York' },
                { name: 'Central Time', zone: 'America/Chicago' },
                { name: 'Mountain Time', zone: 'America/Denver' },
                { name: 'Pacific Time', zone: 'America/Los_Angeles' },
                { name: 'London', zone: 'Europe/London' },
                { name: 'Paris', zone: 'Europe/Paris' },
                { name: 'Tokyo', zone: 'Asia/Tokyo' }
            ];

            let result = `Converting from ${fromTimezone}:\n\n`;

            timezones.forEach(tz => {
                try {
                    const convertedTime = new Intl.DateTimeFormat('en-US', {
                        timeZone: tz.zone,
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    }).format(date);
                    result += `${tz.name}: ${convertedTime}\n`;
                } catch (error) {
                    result += `${tz.name}: Error\n`;
                }
            });

            document.getElementById('timezoneResult').textContent = result;
        }

        // QR Code Generator (Simplified - shows text representation)
        function generateQR() {
            const text = document.getElementById('qrText').value;
            if (!text) {
                document.getElementById('qrResult').textContent = 'Please enter text to encode.';
                return;
            }

            // This is a simplified representation - in a real app you'd use a QR code library
            document.getElementById('qrResult').textContent =
                `QR Code generated for: "${text}"\n\n` +
                `█████████████████████████\n` +
                `█ ▄▄▄▄▄ █▀█ █ ▄▄▄▄▄ █\n` +
                `█ █   █ █▀▀ █ █   █ █\n` +
                `█ █▄▄▄█ █▀█ █ █▄▄▄█ █\n` +
                `█▄▄▄▄▄▄▄█▄▀▄█▄▄▄▄▄▄▄█\n` +
                `█▄▄█▄▀▄▄▀██▀▀█▄█▄▀▄▄█\n` +
                `█▄▄▄▄▄▄▄█▄▄▄█▄▄▄▄▄▄▄█\n` +
                `█████████████████████████\n\n` +
                `Note: This is a placeholder. Use a real QR code library for actual QR codes.`;
        }

        // URL Shortener Simulator
        function shortenUrl() {
            const longUrl = document.getElementById('longUrl').value;
            if (!longUrl) {
                document.getElementById('urlResult').textContent = 'Please enter a URL to shorten.';
                return;
            }

            // Generate a random short code
            const shortCode = Math.random().toString(36).substring(2, 8);
            const shortUrl = `https://short.ly/${shortCode}`;

            document.getElementById('urlResult').textContent =
                `Original URL: ${longUrl}\nShortened URL: ${shortUrl}\n\nNote: This is a simulation. The shortened URL is not functional.`;
        }

        // Random Number Generator
        function generateRandomNumbers() {
            const min = parseInt(document.getElementById('minNumber').value);
            const max = parseInt(document.getElementById('maxNumber').value);
            const count = parseInt(document.getElementById('numberCount').value);

            if (isNaN(min) || isNaN(max) || isNaN(count)) {
                document.getElementById('randomResult').textContent = 'Please enter valid numbers.';
                return;
            }

            if (min >= max) {
                document.getElementById('randomResult').textContent = 'Minimum must be less than maximum.';
                return;
            }

            const numbers = [];
            for (let i = 0; i < count; i++) {
                numbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
            }

            document.getElementById('randomResult').textContent =
                `Generated ${count} random number(s) between ${min} and ${max}:\n\n${numbers.join(', ')}`;
        }

        // Initialize the current time for timezone converter
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const localISOTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('timeInput').value = localISOTime;
        });
    </script>
</body>
</html>

{"version": "1.4.0", "project": {"name": "Scala_d5c0a6989e-test", "directory": "/Users/<USER>/Documents/Projects/Scala/.scala-build", "workspaceDir": "/Users/<USER>/Documents/Projects/Scala", "sources": [], "dependencies": ["Scala_d5c0a6989e"], "classpath": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.7.0/scala3-library_3-3.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "/Users/<USER>/Documents/Projects/Scala/.scala-build/Scala_d5c0a6989e/classes/main", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/sourcegraph/semanticdb-javac/0.10.0/semanticdb-javac-0.10.0.jar"], "out": "/Users/<USER>/Documents/Projects/Scala/.scala-build/.bloop/Scala_d5c0a6989e-test", "classesDir": "/Users/<USER>/Documents/Projects/Scala/.scala-build/Scala_d5c0a6989e/classes/test", "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "3.7.0", "options": ["-Xsemanticdb", "-sourceroot", "/Users/<USER>/Documents/Projects/Scala"], "jars": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.7.0/scala3-compiler_3-3.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.7.0/scala3-interfaces-3.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.7.0/scala3-library_3-3.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.7.0/tasty-core_3-3.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.7.1-scala-1/scala-asm-9.7.1-scala-1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.7/compiler-interface-1.10.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.29.0/jline-reader-3.29.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.29.0/jline-terminal-3.29.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.29.0/jline-terminal-jni-3.29.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.10.7/util-interface-1.10.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.29.0/jline-native-3.29.0.jar"], "bridgeJars": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.7.0/scala3-sbt-bridge-3.7.0.jar"]}, "java": {"options": ["-Xplugin:semanticdb -sourceroot:/Users/<USER>/Documents/Projects/Scala -targetroot:javac-classes-directory", "-J--add-exports", "-Jjdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED", "-J--add-exports", "-Jjdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED", "-J--add-exports", "-Jjdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED", "-J--add-exports", "-Jjdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED", "-J--add-exports", "-Jjdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED"]}, "test": {"frameworks": [{"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs.runner.SpecsFramework", "org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["utest.runner.Framework"]}, {"names": ["munit.Framework"]}, {"names": ["mill.testng.TestNGFramework"]}], "options": {"excludes": [], "arguments": []}}, "platform": {"name": "jvm", "config": {"home": "/Users/<USER>/Library/Caches/Coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.14%252B7/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.14_7.tar.gz/jdk-17.0.14+7/Contents/Home", "options": []}, "mainClass": []}, "resolution": {"modules": [{"organization": "org.scala-lang", "name": "scala3-library_3", "version": "3.7.0", "artifacts": [{"name": "scala3-library_3", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.7.0/scala3-library_3-3.7.0.jar"}, {"name": "scala3-library_3", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.7.0/scala3-library_3-3.7.0-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.16", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16-sources.jar"}]}]}, "tags": ["test"]}}